
	  




QUY ĐỊNH VỀ
BẢO MẬT TRONG PHÁT TRIỂN PHẦN MỀM

01
Bảo mật trong phfit triển phần mềm


M<PERSON><PERSON> đ<PERSON>ch


	•	<PERSON><PERSON><PERSON> bảo an toàn an ninh thông tin được thực hiện trong vòng đời phát triển phần mềm cho các ứng dụng và hệ thống thông tin mà SotaTek chịu trách nhiệm phát triển.







Phạm vi fip dụng

	•	Tất cả các sản phẩm phần mềm dịch vụ được phát triển bởi khối sản xuất của SotaTek và các bộ phận chịu trách nhiệm quản lý hệ thống thông tin của SotaTek.

	•	Toàn bộ sản phẩm thông tin do khách hàng cung cấp, mã nguồn mở mà khối sản xuất sử dụng trong trong trình phát triển.


	•	Đ<PERSON>h gifi bảo mật phần mềm

	•	Nghiêm trọng
Các lỗ hổng cần có biện pháp xử lý ngay.
Nghiêm trọng
Các lỗ hổng cần có biện pháp xử lý ngay.

	•	Trung bình
Các lỗ hổng ít nghiêm trọng hơn, có thể xử lý sau nhưng cần lên kế hoạch xử lý.
Trung bình
Các lỗ hổng ít nghiêm trọng hơn, có thể xử lý sau nhưng cần lên kế hoạch xử lý.

	•	Thấp
Các lỗ hổng kém nghiêm trọng, có thể chấp nhận được.
Thấp
Các lỗ hổng kém nghiêm trọng, có thể chấp nhận được.





	•	Vfi lỗi
	•	Đfinh gifi bảo mật phần mềm


Các hệ thống thông tin quản lý dự án của SotaTek phải được cài đặt các bản vá lỗi bảo mật để bảo vệ tài sản khỏi các lỗ hổng đã biết.




Cập nhật tự động: Tất cả các hệ thống phải được kích hoạt tính năng cập nhật phần mềm tự động cho các bản vá lỗi.


	•	Bảo mật vòng đời phfit triển phần mềm
Bảo mật an toàn khi viết source code (SC)
Bảo mật an toàn khi viết source code (SC)



01
01
Review, Đánh giá: SC sau khi được phát triển cần phải được review, rà soát bởi người chịu trách nhiệm và người được phân công.




02
02
03
03
04
04
Tool quản lý, lưu trữ SC và các tài liệu liên quan đến SC phải được ghi chép cụ thể trong văn bản quản lý cấu hình của dự án.


	•	Bảo mật vòng đời phfit triển phần mềm
Bảo mật an toàn khi viết source code (SC)
Bảo mật an toàn khi viết source code (SC)






NOTE
Nếu khách hàng cho phép sử dụng mã/thư viện mã nguồn mở hoặc SC của bên thứ 3, dự án phải tuân theo thỏa thuận cấp phép của mã/thư viện đó và phải lấy mã cấp phép nếu cần.


Nếu khách hàng không chỉ định nhưng cũng không cấm sử dụng bất kỳ mã/thư viện mã nguồn mở hoặc SC của bên thứ 3 nào, quản lý dự án, dự án có thể tạo báo cáo đánh giá DAR (decision analysis resolution) để lựa chọn mã/thư viện mã nguồn mở hoặc SC của bên thứ 3 phù hợp để sử dụng.


2. Bảo mật vòng đời phfit triển phần mềm
Bảo mật an toàn khi viết source code (SC)
Bảo mật an toàn khi viết source code (SC)






NOTE
Nếu khách hàng không cho phép sử dụng bất kỳ mã/thư viện mã nguồn mở hoặc SC của bên thứ 3 nào, dự án tuyệt đối không được sử dụng bất kỳ mã nguồn/thư viện bên ngoài nào trong mã do dự án tạo ra.


Bảo vệ dữ liệu trong qufi trình lưu trữ và bàn giao
Bảo vệ dữ liệu trong qufi trình lưu trữ và bàn giao





01
Các thông tin trước khi bàn giao cho khách hàng phải được tuyệt đối lưu trữ an toàn và chỉ phân quyền nhân viên dự án đó được phép truy cập và sử dụng.
01
Các thông tin trước khi bàn giao cho khách hàng phải được tuyệt đối lưu trữ an toàn và chỉ phân quyền nhân viên dự án đó được phép truy cập và sử dụng.
02
Khi bàn giao thông tin cho khách hàng, người chịu trách nhiệm bàn giao cần đảm bảo bàn giao đúng thông tin ứng với mốc bàn giao đã thống nhất với khách hàng trước đó.
02
Khi bàn giao thông tin cho khách hàng, người chịu trách nhiệm bàn giao cần đảm bảo bàn giao đúng thông tin ứng với mốc bàn giao đã thống nhất với khách hàng trước đó.

02
Một số Quy định Bảo mật thông tin


Quy định Bảo mật thông tin

Source code: Chỉ sử dụng Github của Công ty cấp để lưu trữ, commit SC trước khi đẩy sang cho khách hàng

Khi tạo repositories mới, PM phải yêu cầu và xin phê duyệt người có thẩm quyền tạo repo: CTO, DD, Vice DD

Khi khách hàng yêu cầu sử dụng github của khách, phải đảm bảo tuân thủ đúng các quy định sử dụng tool của khách hàng
Phải dùng account của tổ chức theo cấu trúc Account
 name/sotatek-dev để push SC, tạo pull request, commit, merge trên Github


Quy định Bảo mật thông tin

Chỉ được phép cài đặt những phần mềm trong Phụ lục danh mục cfic phần mềm được phép cài đặt trên mfiy tính ISMS.REG.005_Quy dinh ve may tinh
CBNV có nhu cầu cài đặt thêm các phầm mềm khác thì phải có email  xác nhận của Trưởng/phó bộ phận, cc cho bộ phận IT và trưởng bộ
phận ISMS để đánh giá rủi ro và cập nhật danh mục nếu có

Hành động cố tình can thiệp thay đổi quyền quản trị, thay đổi thông số không qua CNTT sẽ xem là hành động cố tình tạo rủi ro ATTT


Quy định Bảo mật thông tin

QĐ làm việc bên ngoài: cá nhân cần có sự đồng thuận từ phía khách hàng và phải đảm bảo dữ liệu được mang đi trong trạng thái đã mã hóa
QĐ sử dụng mail: Khi đính kèm dữ liệu trong mail cần thực hiện mã hóa (thông qua một phương tiện khác) gửi đến người nhận
QĐ QL tài sản: Trước khi cho mượn tài sản, cần xác nhận tài sản không chưa thông tin cá nhân hoặc dữ liệu dạng raw
QĐ mã hóa: Khi thực hiện trích xuất dữ liệu khỏi môi trường phát triển, phải thực hiện việc mã hóa với mức độ bảo mật cao

03
Quy định xử phạt và một số
hành vi vi phạm an toàn bảo mật thông tin


	Quy định xử lý vi phạm an toàn BMTT
Quy định xử lý vi phạm an toàn BMTT

CĂN CỨ VÀO: Mức độ rủi ro: Rò rỉ/ mất mát thông tin
Mức độ thiệt hại thực tế: Vật chất/ uy tín công ty
Tính chất lỗi: Cố ý/ Vô ý

Một hoặc tất cả cfic hình thức sau:
	•	Đánh giá bằng điểm PA hàng tháng
	•	Phạt tiền mặt: từ 100.000đ đến 1.000.000đ (cụ thể tại Quy định xử lý vi phạm ATTT)
	•	Bồi thường thiệt hại theo quy định pháp luật
	•	Kỷ luật lao động theo Nội quy lao động của Công ty
Nặng nhất là buộc thôi việc, đưa ra tòa fin, xfic định bồi thường



Một số ví dụ về tình huống xảy ra mất ATTT


	•	Ví dụ về sự cố có thể xảy ra
	•	Ảnh hưởng của sự cố đối với công ty
	•	Biện phfip khắc phục, phòng ngừa



Sự cố 1

Dự án sử dụng tool autobuild trên github để cải thiện tốc độ build server. Member trong dự án sơ suất khi đẩy ﬁle conﬁg lên server không check lại trong ﬁle có chứa key dùng để access AWS mà công ty và KH đang sử dụng, dẫn tới việc có user ẩn danh đã access và sử dụng các dịch vụ trên AWS.


Ảnh hưởng

Ảnh hưởng
Khi lộ thông tin của access key này, dẫn đến 2 việc:

Khách hàng có thể claim về việc không thể upload ﬁle đính kèm do sau khi xóa access key account, việc này gây trở ngại cho production.
Anonymous có thể sử dụng để access vào các services của AWS và dẫn đến thiệt hại về chi phí, mất thông tin quan trọng của KH.
Sự cố nghiêm trọng, công ty tốn nhiều thời gian, công sức để giải quyết.

Ảnh hưởng tới hình ảnh công ty.


Hành động khắc phục/ phòng ngừa

	Disable key đã bị public trên AWS.
	Xóa hẳn account ẩn danh access và toàn bộ tool autobuild trên github.
Xin lỗi khách hàng.

Xử phạt cá nhân vi phạm theo quy định công ty.

Họp nhắc nhở toàn dự án về sự cố đã xảy ra, yêu cầu nâng cao ý thức bảo mật.
Định kỳ dự án tổ chức phân tích các vấn đề liên quan tới security và training cho tất cả member, đặc biệt các member mới.


Sự cố 2


Trong 1 lần khách hàng kiểm tra lại log truy cập Internet của các nhân viên dự án, khách hàng phát hiện ra 1 nhân viên upload tài liệu mật có chứa các thông tin tài sản của khách hàng lên một web service public.



Ảnh hưởng
Ảnh hưởng

 Khách hàng mất niềm tin vào công ty, có thể mất dự án.
Dự án bị trì hoãn để xử lý sự cố.

Công ty tốn nhiều effort để giải quyết.

Gây thiệt hại về tài chính.

Hình ảnh của Công ty bị ảnh hưởng.

Nguy cơ rò rỉ thông tin do upload tài liệu lên internet.



Hành động khắc phục/ phòng ngừa


	Xóa ﬁle tài liệu mật đang lưu trên web service.
	Kiểm tra ngay trên các trang khác có lưu ﬁle tài liệu đó hay không để xử lý. 	Đổi mật khẩu tài khoản hoặc disable tài khoản đó.
 Xin lỗi khách hàng.
Xem xét mức độ ảnh hưởng để đưa ra hình thức xử lý NV vi phạm.



THAM CHIẾU

Toàn bộ quy định, quy trình đảm bảo An toàn và Bảo mật thông tin được cập nhật tại link sau, mọi người vui lòng đọc kỹ và tuân thủ:

[SOTATEK] ISMS

* Lưu ý: Dùng tài khoản email của công ty để đăng nhập.


Thank yOu!
