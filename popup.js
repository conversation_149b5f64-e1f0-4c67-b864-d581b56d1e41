document.addEventListener("DOMContentLoaded", function () {
  const apiKeyInput = document.getElementById("apiKey");
  const saveKeyBtn = document.getElementById("saveKey");
  const answerQuizBtn = document.getElementById("answerQuiz");
  const status = document.getElementById("status");

  // Load saved API key
  chrome.storage.sync.get(["openaiApiKey"], function (result) {
    if (result.openaiApiKey) {
      apiKeyInput.value = result.openaiApiKey;
    }
  });

  saveKeyBtn.addEventListener("click", function () {
    const apiKey = apiKeyInput.value.trim();
    if (apiKey) {
      chrome.storage.sync.set({ openaiApiKey: apiKey }, function () {
        status.textContent = "API Key saved!";
        setTimeout(() => (status.textContent = ""), 2000);
      });
    }
  });

  answerQuizBtn.addEventListener("click", function () {
    chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
      chrome.tabs.sendMessage(
        tabs[0].id,
        { action: "answerQuiz" },
        function (response) {
          if (chrome.runtime.lastError) {
            status.textContent = "Error: Please refresh the page and try again";
            console.error(
              "Extension connection error:",
              chrome.runtime.lastError.message
            );
            return;
          }

          if (response && response.success) {
            status.textContent = "Quiz answered!";
          } else {
            status.textContent =
              "Error: " + (response?.error || "Unknown error");
          }
        }
      );
    });
  });
});
