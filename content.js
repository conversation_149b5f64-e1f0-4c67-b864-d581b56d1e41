chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  if (request.action === "answerQuiz") {
    answerQuiz()
      .then((result) => {
        sendResponse(result);
      })
      .catch((error) => {
        sendResponse({ success: false, error: error.message });
      });
    return true; // Keep message channel open for async response
  }
});

async function answerQuiz() {
  try {
    // Get API key from storage
    const result = await new Promise((resolve) => {
      chrome.storage.sync.get(["openaiApiKey"], resolve);
    });

    if (!result.openaiApiKey) {
      throw new Error("Please set your OpenAI API key first");
    }

    // Find all questions on the page
    const questions = extractQuestions();

    if (questions.length === 0) {
      throw new Error("No questions found on this page");
    }

    // Answer each question
    for (const question of questions) {
      await answerSingleQuestion(question, result.openaiApiKey);
    }

    return { success: true, answeredCount: questions.length };
  } catch (error) {
    console.error("Quiz answering failed:", error);
    return { success: false, error: error.message };
  }
}

function extractQuestions() {
  const questions = [];

  // Look for question containers
  const questionElements = document.querySelectorAll("p");

  questionElements.forEach((questionEl) => {
    const questionText = questionEl.textContent.trim();

    // Check if this looks like a question
    if (
      questionText.includes("Question") ||
      questionText.includes("Câu hỏi") ||
      questionText.includes(":")
    ) {
      const questionContainer =
        questionEl.closest(".flex.flex-col.gap-4") || questionEl.parentElement;

      if (questionContainer) {
        const checkboxes = questionContainer.querySelectorAll(
          'button[role="checkbox"]'
        );
        const labels = questionContainer.querySelectorAll("label");

        if (checkboxes.length > 0 && labels.length > 0) {
          const options = [];

          labels.forEach((label, index) => {
            const labelText = label.textContent.trim();
            if (labelText && !labelText.includes("There must be at least")) {
              options.push({
                text: labelText,
                checkbox: checkboxes[index],
                label: label,
              });
            }
          });

          if (options.length > 0) {
            questions.push({
              questionText: questionText,
              options: options,
              container: questionContainer,
            });
          }
        }
      }
    }
  });

  return questions;
}

function extractCourseContent() {
  try {
    // Look for course content in various common selectors
    const contentSelectors = [
      ".course-content",
      ".lecture-content",
      ".lesson-content",
      ".content-body",
      ".main-content",
      "article",
      ".text-content",
      '[class*="content"]',
      "main",
    ];

    let courseContent = "";

    // Try to find course content
    for (const selector of contentSelectors) {
      const elements = document.querySelectorAll(selector);
      for (const element of elements) {
        const text = element.textContent?.trim();
        if (
          text &&
          text.length > 100 &&
          !text.includes("Question") &&
          !text.includes("Câu hỏi")
        ) {
          courseContent += text + "\n\n";
          if (courseContent.length > 3000) break; // Limit content length
        }
      }
      if (courseContent.length > 500) break; // Found enough content
    }

    // If no specific content found, try to get general page text
    if (!courseContent) {
      const bodyText = document.body.textContent || "";
      const lines = bodyText.split("\n").filter((line) => {
        const trimmed = line.trim();
        return (
          trimmed.length > 50 &&
          !trimmed.includes("Question") &&
          !trimmed.includes("Câu hỏi") &&
          !trimmed.includes("button") &&
          !trimmed.includes("click")
        );
      });
      courseContent = lines.slice(0, 10).join("\n");
    }

    // Clean and limit the content
    return courseContent.substring(0, 2000).trim();
  } catch (error) {
    console.error("Error extracting course content:", error);
    return "";
  }
}

// SOTATEK ISMS & Software Development Security Reference
const SOTATEK_SECURITY_REFERENCE = `
SOTATEK INFORMATION SECURITY MANAGEMENT SYSTEM (ISMS) REFERENCE

ORGANIZATIONAL SECURITY FRAMEWORK:
- ISMS Implementation: ISO 27001:2022 compliance with SDLC integration
- Security Governance: Information Security Committee, ISO, Department Coordinators
- Policy Framework: Master policy, specific policies, procedures, standards
- Vietnamese Cybersecurity Law compliance and international standards

RISK MANAGEMENT FRAMEWORK:
- Risk Assessment: Asset identification → Threat analysis → Vulnerability assessment → Impact analysis
- Risk Treatment: Mitigation, Acceptance, Avoidance, Transfer
- Risk Monitoring: Quarterly reviews, annual assessments, incident-driven reviews
- Control Testing: Regular validation of implemented controls

SOFTWARE DEVELOPMENT SECURITY (HR MANUAL):
- SDLC Integration: Security in Requirements, Design, Implementation, Testing, Deployment, Maintenance
- Developer Training: Mandatory security training, secure coding, security testing, incident response
- Code Standards: Input validation, output encoding, authentication, authorization, session management
- Development Environment: Access controls, source code protection, build pipeline security, dependency management

INCIDENT MANAGEMENT PROCEDURES:
- Classification: Level 1 (Low), Level 2 (Medium), Level 3 (High), Level 4 (Critical)
- Response Team: Incident Commander, Technical Lead, Communications Lead, Legal/Compliance, Business Rep
- Process: Detection → Containment → Eradication → Recovery → Lessons Learned
- Reporting: 1-hour internal reporting, management notification, regulatory compliance, customer notification

ACCESS CONTROL & IDENTITY MANAGEMENT:
- User Access: Formal provisioning, quarterly reviews, immediate deprovisioning
- Privileged Access: Enhanced controls for administrative accounts
- Authentication: Multi-factor authentication for critical systems
- Authorization: Role-based access control with least privilege principle

PHYSICAL & ENVIRONMENTAL SECURITY:
- Facility Security: Controlled access, visitor management, surveillance systems
- Equipment Security: Asset inventory, secure disposal, maintenance procedures
- Environmental Controls: Fire suppression, climate control, power management
- Clean Desk Policy: Mandatory for all workstations and common areas

DATA PROTECTION & PRIVACY:
- Data Classification: Public, Internal, Confidential, Restricted categories
- Data Handling: Encryption requirements, retention policies, disposal procedures
- Privacy Compliance: Vietnamese Personal Data Protection regulations
- Cross-border Transfer: Approval required for international data transfers

BUSINESS CONTINUITY & DISASTER RECOVERY:
- BCP Framework: Business Impact Analysis, Recovery strategies, Testing procedures
- RTO/RPO Targets: Critical systems 4 hours RTO, 1 hour RPO
- Backup Procedures: Daily incremental, weekly full, monthly offsite
- Crisis Management: Emergency response team, communication procedures

COMPLIANCE & AUDIT:
- Internal Audits: Annual ISMS audit, quarterly control testing
- External Audits: ISO 27001 certification audit, regulatory compliance audits
- Management Reviews: Quarterly ISMS performance reviews
- Corrective Actions: 30-day resolution for major findings, 90-day for minor findings

SECURITY AWARENESS & TRAINING:
- Annual Training: Mandatory for all employees, role-specific modules
- Phishing Simulation: Monthly campaigns with remedial training
- Security Communications: Weekly security tips, incident notifications
- New Employee Orientation: Security briefing within first week of employment
`;

async function answerSingleQuestion(question, apiKey) {
  try {
    // Extract course content from the page for context
    const courseContent = extractCourseContent();

    const prompt = `You are answering a quiz question about SOTATEK's Information Security Management System (ISMS) and Software Development Security policies. Use the comprehensive SOTATEK reference material and course content to provide the most accurate answer based on the organization's specific policies and procedures.

SOTATEK ISMS & SOFTWARE DEVELOPMENT SECURITY REFERENCE:
${SOTATEK_SECURITY_REFERENCE}

${
  courseContent ? `COURSE CONTENT REFERENCE:\n${courseContent}\n\n` : ""
}QUESTION: ${question.questionText}

OPTIONS:
${question.options
  .map((opt, i) => `${String.fromCharCode(65 + i)}. ${opt.text}`)
  .join("\n")}

Based on SOTATEK's specific ISMS policies, software development security requirements, and course content above, analyze the question and return only the single letter of the BEST correct answer (A, B, C, D, etc.) that aligns with SOTATEK's organizational policies and procedures.`;

    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content:
              "You are an expert in SOTATEK's Information Security Management System (ISMS) and Software Development Security policies. Use the comprehensive SOTATEK reference material provided in the user prompt to answer quiz questions accurately based on the organization's specific policies, procedures, and requirements. The reference includes SOTATEK's ISMS implementation, risk management framework, software development security requirements from the HR manual, incident management procedures, access controls, and compliance requirements. Always respond with just ONE letter (A, B, C, or D) for the single best answer that aligns with SOTATEK's organizational policies and procedures. Never provide multiple answers.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: 0,
        max_tokens: 100,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    const answer = data.choices[0].message.content.trim();

    // Select the correct answer(s)
    selectAnswers(question, answer);

    // Small delay between questions
    await new Promise((resolve) => setTimeout(resolve, 500));
  } catch (error) {
    console.error("Error answering question:", error);
    throw error;
  }
}

function selectAnswers(question, answer) {
  // First, uncheck all options
  question.options.forEach((option) => {
    if (option.checkbox.getAttribute("aria-checked") === "true") {
      option.checkbox.click();
    }
  });

  // Try to match by letter (A, B, C, D) - select only the FIRST match
  const letterMatch = answer.match(/[A-Z]/);
  if (letterMatch) {
    const letter = letterMatch[0]; // Take only the first letter
    const index = letter.charCodeAt(0) - 65; // A=0, B=1, etc.
    if (index >= 0 && index < question.options.length) {
      question.options[index].checkbox.click();
      return; // Exit after selecting one answer
    }
  }

  // Try to match by text content - select only the FIRST match
  for (const option of question.options) {
    if (
      answer.toLowerCase().includes(option.text.toLowerCase().substring(0, 20))
    ) {
      option.checkbox.click();
      return; // Exit after selecting one answer
    }
  }
}
