chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === 'answerQuiz') {
    answerQuiz().then(result => {
      sendResponse(result);
    }).catch(error => {
      sendResponse({success: false, error: error.message});
    });
    return true; // Keep message channel open for async response
  }
});

async function answerQuiz() {
  try {
    // Get API key from storage
    const result = await new Promise(resolve => {
      chrome.storage.sync.get(['openaiApiKey'], resolve);
    });
    
    if (!result.openaiApiKey) {
      throw new Error('Please set your OpenAI API key first');
    }

    // Find all questions on the page
    const questions = extractQuestions();
    
    if (questions.length === 0) {
      throw new Error('No questions found on this page');
    }

    // Answer each question
    for (const question of questions) {
      await answerSingleQuestion(question, result.openaiApiKey);
    }

    return {success: true, answeredCount: questions.length};
  } catch (error) {
    console.error('Quiz answering failed:', error);
    return {success: false, error: error.message};
  }
}

function extractQuestions() {
  const questions = [];
  
  // Look for question containers
  const questionElements = document.querySelectorAll('p');
  
  questionElements.forEach(questionEl => {
    const questionText = questionEl.textContent.trim();
    
    // Check if this looks like a question
    if (questionText.includes('Question') || questionText.includes('Câu hỏi') || questionText.includes(':')) {
      const questionContainer = questionEl.closest('.flex.flex-col.gap-4') || questionEl.parentElement;
      
      if (questionContainer) {
        const checkboxes = questionContainer.querySelectorAll('button[role="checkbox"]');
        const labels = questionContainer.querySelectorAll('label');
        
        if (checkboxes.length > 0 && labels.length > 0) {
          const options = [];
          
          labels.forEach((label, index) => {
            const labelText = label.textContent.trim();
            if (labelText && !labelText.includes('There must be at least')) {
              options.push({
                text: labelText,
                checkbox: checkboxes[index],
                label: label
              });
            }
          });
          
          if (options.length > 0) {
            questions.push({
              questionText: questionText,
              options: options,
              container: questionContainer
            });
          }
        }
      }
    }
  });
  
  return questions;
}

async function answerSingleQuestion(question, apiKey) {
  try {
    const prompt = `Answer this multiple choice question. Return only the letter(s) of the correct answer(s) (A, B, C, D, etc.) or the exact text of the correct option(s). If multiple answers are correct, separate them with commas.

Question: ${question.questionText}

Options:
${question.options.map((opt, i) => `${String.fromCharCode(65 + i)}. ${opt.text}`).join('\n')}`;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant that answers quiz questions accurately. Always respond with just the letter(s) or exact text of the correct answer(s).'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0,
        max_tokens: 100
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    const answer = data.choices[0].message.content.trim();
    
    // Select the correct answer(s)
    selectAnswers(question, answer);
    
    // Small delay between questions
    await new Promise(resolve => setTimeout(resolve, 500));
    
  } catch (error) {
    console.error('Error answering question:', error);
    throw error;
  }
}

function selectAnswers(question, answer) {
  // First, uncheck all options
  question.options.forEach(option => {
    if (option.checkbox.getAttribute('aria-checked') === 'true') {
      option.checkbox.click();
    }
  });

  // Try to match by letter (A, B, C, D)
  const letterMatches = answer.match(/[A-Z]/g);
  if (letterMatches) {
    letterMatches.forEach(letter => {
      const index = letter.charCodeAt(0) - 65; // A=0, B=1, etc.
      if (index >= 0 && index < question.options.length) {
        question.options[index].checkbox.click();
      }
    });
    return;
  }

  // Try to match by text content
  question.options.forEach(option => {
    if (answer.toLowerCase().includes(option.text.toLowerCase().substring(0, 20))) {
      option.checkbox.click();
    }
  });
}