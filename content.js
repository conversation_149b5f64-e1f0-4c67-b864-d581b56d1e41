chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  if (request.action === "answerQuiz") {
    answerQuiz()
      .then((result) => {
        sendResponse(result);
      })
      .catch((error) => {
        sendResponse({ success: false, error: error.message });
      });
    return true; // Keep message channel open for async response
  }
});

async function answerQuiz() {
  try {
    // Get API key from storage
    const result = await new Promise((resolve) => {
      chrome.storage.sync.get(["openaiApiKey"], resolve);
    });

    if (!result.openaiApiKey) {
      throw new Error("Please set your OpenAI API key first");
    }

    // Find all questions on the page
    const questions = extractQuestions();

    if (questions.length === 0) {
      throw new Error("No questions found on this page");
    }

    // Answer each question
    for (const question of questions) {
      await answerSingleQuestion(question, result.openaiApiKey);
    }

    return { success: true, answeredCount: questions.length };
  } catch (error) {
    console.error("Quiz answering failed:", error);
    return { success: false, error: error.message };
  }
}

function extractQuestions() {
  const questions = [];

  // Look for question containers
  const questionElements = document.querySelectorAll("p");

  questionElements.forEach((questionEl) => {
    const questionText = questionEl.textContent.trim();

    // Check if this looks like a question
    if (
      questionText.includes("Question") ||
      questionText.includes("Câu hỏi") ||
      questionText.includes(":")
    ) {
      const questionContainer =
        questionEl.closest(".flex.flex-col.gap-4") || questionEl.parentElement;

      if (questionContainer) {
        const checkboxes = questionContainer.querySelectorAll(
          'button[role="checkbox"]'
        );
        const labels = questionContainer.querySelectorAll("label");

        if (checkboxes.length > 0 && labels.length > 0) {
          const options = [];

          labels.forEach((label, index) => {
            const labelText = label.textContent.trim();
            if (labelText && !labelText.includes("There must be at least")) {
              options.push({
                text: labelText,
                checkbox: checkboxes[index],
                label: label,
              });
            }
          });

          if (options.length > 0) {
            questions.push({
              questionText: questionText,
              options: options,
              container: questionContainer,
            });
          }
        }
      }
    }
  });

  return questions;
}

function extractCourseContent() {
  try {
    // Look for course content in various common selectors
    const contentSelectors = [
      ".course-content",
      ".lecture-content",
      ".lesson-content",
      ".content-body",
      ".main-content",
      "article",
      ".text-content",
      '[class*="content"]',
      "main",
    ];

    let courseContent = "";

    // Try to find course content
    for (const selector of contentSelectors) {
      const elements = document.querySelectorAll(selector);
      for (const element of elements) {
        const text = element.textContent?.trim();
        if (
          text &&
          text.length > 100 &&
          !text.includes("Question") &&
          !text.includes("Câu hỏi")
        ) {
          courseContent += text + "\n\n";
          if (courseContent.length > 3000) break; // Limit content length
        }
      }
      if (courseContent.length > 500) break; // Found enough content
    }

    // If no specific content found, try to get general page text
    if (!courseContent) {
      const bodyText = document.body.textContent || "";
      const lines = bodyText.split("\n").filter((line) => {
        const trimmed = line.trim();
        return (
          trimmed.length > 50 &&
          !trimmed.includes("Question") &&
          !trimmed.includes("Câu hỏi") &&
          !trimmed.includes("button") &&
          !trimmed.includes("click")
        );
      });
      courseContent = lines.slice(0, 10).join("\n");
    }

    // Clean and limit the content
    return courseContent.substring(0, 2000).trim();
  } catch (error) {
    console.error("Error extracting course content:", error);
    return "";
  }
}

// ISO 27001:2022 Comprehensive Reference
const ISO27001_REFERENCE = `
ISO 27001:2022 INFORMATION SECURITY MANAGEMENT REFERENCE

FUNDAMENTAL CONCEPTS:
- ISMS: Systematic approach to managing sensitive information (people, processes, IT systems)
- CIA Triad: Confidentiality, Integrity, Availability
- PDCA Cycle: Plan-Do-Check-Act for continuous improvement
- Risk Management: Assessment → Treatment → Acceptance → Communication

STRUCTURE (10 CLAUSES):
4. Context of Organization: Understanding context, scope, stakeholder needs
5. Leadership: Leadership commitment, policy, roles, responsibilities
6. Planning: Risk assessment/treatment, objectives, change planning
7. Support: Resources, competence, awareness, communication, documentation
8. Operation: Operational planning, risk implementation, incident management
9. Performance Evaluation: Monitoring, internal audits, management review
10. Improvement: Nonconformity, corrective action, continual improvement

ANNEX A CONTROLS (93 total):
A.5 Organizational (37): Policies, roles, responsibilities, segregation of duties
A.6 People (8): Screening, employment terms, training, awareness, discipline
A.7 Physical (14): Perimeters, access controls, environmental protection
A.8 Technology (34): Access management, cryptography, system security, malware

RISK MANAGEMENT PROCESS:
1. Risk Assessment: Identify threats/vulnerabilities → Analyze likelihood/impact → Evaluate risk levels
2. Risk Treatment Options: Modify (controls), Retain (accept), Avoid (eliminate), Share (transfer)
3. Risk Acceptance: Formal approval of residual risks by management
4. Risk Communication: Information sharing with relevant stakeholders

INCIDENT MANAGEMENT:
Process: Preparation → Detection → Analysis → Containment → Eradication → Recovery → Lessons Learned
Classification: Low (minimal impact) → Medium (moderate) → High (significant) → Critical (severe)

BUSINESS CONTINUITY:
- BIA: Business Impact Analysis to identify critical processes
- RTO: Recovery Time Objective (maximum acceptable downtime)
- RPO: Recovery Point Objective (maximum acceptable data loss)
- Regular testing and plan updates required

COMPLIANCE & AUDIT:
- Internal audits at planned intervals
- Management reviews at planned intervals
- Corrective actions for nonconformities
- Continuous monitoring and improvement
- Legal and regulatory compliance monitoring

SOFTWARE DEVELOPMENT SECURITY:
- Secure SDLC: Requirements → Design → Code → Test → Deploy
- Application Security: Input validation, authentication, authorization, error handling
- Security Testing: SAST, DAST, IAST, SCA, penetration testing
- Development Environment: Secure environments, source control, build pipelines
`;

async function answerSingleQuestion(question, apiKey) {
  try {
    // Extract course content from the page for context
    const courseContent = extractCourseContent();

    const prompt = `You are answering a quiz question about Information Security Management according to ISO 27001:2022. Use the comprehensive reference material and course content to provide the most accurate answer.

COMPREHENSIVE ISO 27001:2022 REFERENCE:
${ISO27001_REFERENCE}

${
  courseContent ? `COURSE CONTENT REFERENCE:\n${courseContent}\n\n` : ""
}QUESTION: ${question.questionText}

OPTIONS:
${question.options
  .map((opt, i) => `${String.fromCharCode(65 + i)}. ${opt.text}`)
  .join("\n")}

Based on the ISO 27001:2022 reference material and course content above, analyze the question and return only the single letter of the BEST correct answer (A, B, C, D, etc.).`;

    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content:
              "You are an expert in Information Security Management and ISO 27001:2022 standards. Use the comprehensive ISO 27001:2022 reference material provided in the user prompt to answer quiz questions accurately. The reference includes detailed information about ISMS concepts, risk management, Annex A controls, incident management, business continuity, and compliance requirements. Always respond with just ONE letter (A, B, C, or D) for the single best answer based on the reference material. Never provide multiple answers.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: 0,
        max_tokens: 100,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    const answer = data.choices[0].message.content.trim();

    // Select the correct answer(s)
    selectAnswers(question, answer);

    // Small delay between questions
    await new Promise((resolve) => setTimeout(resolve, 500));
  } catch (error) {
    console.error("Error answering question:", error);
    throw error;
  }
}

function selectAnswers(question, answer) {
  // First, uncheck all options
  question.options.forEach((option) => {
    if (option.checkbox.getAttribute("aria-checked") === "true") {
      option.checkbox.click();
    }
  });

  // Try to match by letter (A, B, C, D) - select only the FIRST match
  const letterMatch = answer.match(/[A-Z]/);
  if (letterMatch) {
    const letter = letterMatch[0]; // Take only the first letter
    const index = letter.charCodeAt(0) - 65; // A=0, B=1, etc.
    if (index >= 0 && index < question.options.length) {
      question.options[index].checkbox.click();
      return; // Exit after selecting one answer
    }
  }

  // Try to match by text content - select only the FIRST match
  for (const option of question.options) {
    if (
      answer.toLowerCase().includes(option.text.toLowerCase().substring(0, 20))
    ) {
      option.checkbox.click();
      return; // Exit after selecting one answer
    }
  }
}
