// SOTATEK ISMS & Software Development Security Reference
// Based on organizational policies: ISMS Information Security Regulations & HR Software Development Security Manual

const SOTATEK_SECURITY_REFERENCE = `
# SOTATEK INFORMATION SECURITY MANAGEMENT SYSTEM (ISMS) REFERENCE

## 1. ORGANIZATIONAL SECURITY FRAMEWORK

### ISMS Implementation at SOTATEK
- Comprehensive information security management system following ISO 27001:2022
- Integration with software development lifecycle (SDLC)
- Risk-based approach to information security
- Continuous monitoring and improvement processes
- Compliance with Vietnamese cybersecurity laws and international standards

### Information Security Governance
- **Information Security Committee**: Senior management oversight and decision-making
- **Information Security Officer (ISO)**: Day-to-day management and coordination
- **Department Security Coordinators**: Local implementation and compliance
- **All Employees**: Individual responsibility for security practices

### Security Policy Framework
- **Master Information Security Policy**: Top-level organizational commitment
- **Specific Security Policies**: Detailed requirements for different areas
- **Procedures and Guidelines**: Step-by-step implementation instructions
- **Standards and Baselines**: Technical configuration requirements

## 2. SOTATEK RISK MANAGEMENT FRAMEWORK

### Risk Assessment Process
- **Asset Identification**: Catalog all information assets and their owners
- **Threat Analysis**: Identify potential threats to each asset category
- **Vulnerability Assessment**: Evaluate weaknesses in current controls
- **Impact Analysis**: Determine potential business impact of security incidents
- **Risk Calculation**: Combine threat likelihood with potential impact
- **Risk Prioritization**: Rank risks based on organizational risk appetite

### Risk Treatment Options
- **Risk Mitigation**: Implement controls to reduce risk to acceptable levels
- **Risk Acceptance**: Formally accept risks that are within tolerance
- **Risk Avoidance**: Eliminate activities that create unacceptable risks
- **Risk Transfer**: Use insurance or outsourcing to transfer risk

### Risk Monitoring and Review
- **Quarterly Risk Reviews**: Regular assessment of risk landscape changes
- **Annual Risk Assessment**: Comprehensive review of all identified risks
- **Incident-Driven Reviews**: Risk reassessment following security incidents
- **Control Effectiveness Testing**: Regular validation of implemented controls

## 3. SOFTWARE DEVELOPMENT SECURITY (HR MANUAL REQUIREMENTS)

### Secure Development Lifecycle (SDLC) Integration
- **Requirements Phase**: Security requirements definition and approval
- **Design Phase**: Security architecture review and threat modeling
- **Implementation Phase**: Secure coding practices and code reviews
- **Testing Phase**: Security testing including SAST, DAST, and penetration testing
- **Deployment Phase**: Secure configuration and deployment procedures
- **Maintenance Phase**: Ongoing security monitoring and patch management

### Developer Security Training Requirements
- **Mandatory Security Training**: Annual security awareness training for all developers
- **Secure Coding Training**: Language-specific secure coding practices
- **Security Testing Training**: SAST/DAST tools usage and vulnerability remediation
- **Incident Response Training**: Procedures for reporting and handling security incidents
- **Compliance Training**: Understanding of regulatory and organizational requirements

### Code Security Standards
- **Input Validation**: All user inputs must be validated and sanitized
- **Output Encoding**: Proper encoding of all outputs to prevent injection attacks
- **Authentication**: Strong authentication mechanisms for all applications
- **Authorization**: Principle of least privilege for all access controls
- **Session Management**: Secure session handling and timeout procedures
- **Error Handling**: Secure error handling without information disclosure
- **Logging**: Comprehensive security logging for audit and monitoring

### Development Environment Security
- **Access Controls**: Role-based access to development environments and tools
- **Source Code Protection**: Secure version control and code repository management
- **Build Pipeline Security**: Secure CI/CD pipelines with security scanning integration
- **Dependency Management**: Regular scanning and updating of third-party components
- **Environment Isolation**: Separation between development, testing, and production environments

## 4. SOTATEK INCIDENT MANAGEMENT PROCEDURES

### Incident Classification and Response
- **Level 1 (Low)**: Minor security events with minimal business impact
- **Level 2 (Medium)**: Security incidents affecting single systems or small user groups
- **Level 3 (High)**: Major security incidents affecting critical systems or large user groups
- **Level 4 (Critical)**: Severe security breaches requiring immediate executive attention

### Incident Response Team Structure
- **Incident Commander**: Overall incident response coordination and decision-making
- **Technical Lead**: Technical analysis and remediation activities
- **Communications Lead**: Internal and external communications coordination
- **Legal/Compliance Representative**: Legal and regulatory compliance guidance
- **Business Representative**: Business impact assessment and recovery prioritization

### Incident Response Process
1. **Detection and Analysis**: Identify and analyze potential security incidents
2. **Containment**: Immediate actions to prevent incident escalation
3. **Eradication**: Remove the root cause and restore affected systems
4. **Recovery**: Return systems to normal operation with enhanced monitoring
5. **Lessons Learned**: Post-incident review and process improvement

### Incident Reporting Requirements
- **Internal Reporting**: All incidents must be reported to the Information Security Officer within 1 hour
- **Management Notification**: Level 3 and 4 incidents require immediate management notification
- **Regulatory Reporting**: Compliance with Vietnamese cybersecurity law reporting requirements
- **Customer Notification**: Customer notification procedures for incidents affecting customer data

## 5. ACCESS CONTROL AND IDENTITY MANAGEMENT

### User Access Management
- **Account Provisioning**: Formal approval process for all new user accounts
- **Access Reviews**: Quarterly review of all user access rights
- **Account Deprovisioning**: Immediate removal of access upon termination or role change
- **Privileged Access**: Enhanced controls for administrative and system accounts

## 5. INCIDENT MANAGEMENT

### Incident Response Process
- **Preparation**: Establish incident response team and procedures
- **Detection**: Monitor and identify security incidents
- **Analysis**: Assess incident scope and impact
- **Containment**: Limit incident spread and damage
- **Eradication**: Remove threat and vulnerabilities
- **Recovery**: Restore normal operations
- **Lessons Learned**: Document and improve processes

### Incident Classification
- **Low**: Minimal business impact
- **Medium**: Moderate business impact
- **High**: Significant business impact
- **Critical**: Severe business impact requiring immediate action

## 6. BUSINESS CONTINUITY AND DISASTER RECOVERY

### Business Continuity Planning
- Business impact analysis (BIA)
- Risk assessment for continuity
- Continuity strategy development
- Plan implementation and testing
- Plan maintenance and updates

### Disaster Recovery
- Recovery time objectives (RTO)
- Recovery point objectives (RPO)
- Backup and restoration procedures
- Alternative site arrangements
- Communication plans

## 7. COMPLIANCE AND LEGAL REQUIREMENTS

### Legal and Regulatory Compliance
- Identification of applicable laws and regulations
- Compliance monitoring and assessment
- Legal risk management
- Intellectual property protection
- Privacy and data protection requirements

### Audit and Assessment
- Internal audit programs
- External audit coordination
- Compliance reporting
- Corrective action management
- Continuous monitoring

## 8. TRAINING AND AWARENESS

### Security Awareness Programs
- General security awareness training
- Role-specific security training
- Incident response training
- Regular security updates and communications
- Training effectiveness measurement

### Competency Requirements
- Security role competency definitions
- Training needs assessment
- Professional development programs
- Certification requirements
- Knowledge transfer processes

## 9. VENDOR AND THIRD-PARTY MANAGEMENT

### Supplier Security Management
- Supplier security assessment
- Contractual security requirements
- Ongoing supplier monitoring
- Incident notification requirements
- Service level agreements (SLAs)

### Cloud Security
- Cloud service provider assessment
- Data location and sovereignty
- Shared responsibility models
- Cloud security monitoring
- Data portability and deletion

## 10. EMERGING TECHNOLOGIES AND THREATS

### Modern Security Challenges
- Internet of Things (IoT) security
- Artificial Intelligence (AI) and Machine Learning (ML) security
- Mobile device security
- Social engineering attacks
- Advanced persistent threats (APTs)

### Zero Trust Architecture
- Never trust, always verify principle
- Least privilege access
- Micro-segmentation
- Continuous monitoring and validation
- Identity-centric security model
`;

// Export the reference for use in content script
if (typeof module !== "undefined" && module.exports) {
  module.exports = ISO27001_REFERENCE;
}
