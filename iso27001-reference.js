// ISO 27001:2022 Information Security Management Reference
// Comprehensive reference for quiz questions based on course materials

const ISO27001_REFERENCE = `
# ISO 27001:2022 Information Security Management System (ISMS) Reference

## 1. FUNDAMENTAL CONCEPTS

### Information Security Management System (ISMS)
- Systematic approach to managing sensitive company information
- Ensures information remains secure through people, processes, and IT systems
- Based on risk assessment and risk treatment
- Continuous improvement through Plan-Do-Check-Act (PDCA) cycle

### Information Security Objectives
- **Confidentiality**: Information accessible only to authorized individuals
- **Integrity**: Information accuracy and completeness maintained
- **Availability**: Information accessible when needed by authorized users

### Risk Management
- **Risk Assessment**: Identify, analyze, and evaluate information security risks
- **Risk Treatment**: Select and implement appropriate controls
- **Risk Acceptance**: Formally accept residual risks
- **Risk Communication**: Share risk information with stakeholders

## 2. ISO 27001:2022 STRUCTURE

### Clause 4: Context of the Organization
- Understanding organizational context and stakeholder needs
- Determining ISMS scope and boundaries
- Establishing information security policy

### Clause 5: Leadership
- Leadership commitment and responsibility
- Information security policy establishment
- Organizational roles and responsibilities

### Clause 6: Planning
- Risk assessment and treatment planning
- Information security objectives and planning
- Planning for changes to ISMS

### Clause 7: Support
- Resource allocation and competence requirements
- Awareness and communication programs
- Documented information management

### Clause 8: Operation
- Operational planning and control
- Risk assessment and treatment implementation
- Incident management procedures

### Clause 9: Performance Evaluation
- Monitoring, measurement, and analysis
- Internal audit programs
- Management review processes

### Clause 10: Improvement
- Nonconformity and corrective action
- Continual improvement processes

## 3. ANNEX A CONTROLS (ISO 27001:2022)

### A.5 Organizational Controls
- Information security policies and procedures
- Information security roles and responsibilities
- Segregation of duties
- Management responsibilities
- Contact with authorities and special interest groups

### A.6 People Controls
- Screening procedures for personnel
- Terms and conditions of employment
- Information security awareness and training
- Disciplinary processes
- Remote working guidelines

### A.7 Physical and Environmental Security
- Physical security perimeters
- Physical entry controls
- Protection against environmental threats
- Equipment maintenance and disposal
- Secure disposal of media

### A.8 Technology Controls
- User access management
- Privileged access rights
- Information access restriction
- Cryptography policies
- System security and malware protection

## 4. SOFTWARE DEVELOPMENT SECURITY

### Secure Development Lifecycle (SDLC)
- Security requirements analysis
- Secure design principles
- Secure coding practices
- Security testing and validation
- Secure deployment procedures

### Application Security Controls
- Input validation and sanitization
- Output encoding and escaping
- Authentication and session management
- Authorization and access controls
- Error handling and logging

### Code Security Practices
- Static Application Security Testing (SAST)
- Dynamic Application Security Testing (DAST)
- Interactive Application Security Testing (IAST)
- Software Composition Analysis (SCA)
- Penetration testing

### Development Environment Security
- Secure development environments
- Source code management security
- Build and deployment pipeline security
- Third-party component management
- Vulnerability management

## 5. INCIDENT MANAGEMENT

### Incident Response Process
- **Preparation**: Establish incident response team and procedures
- **Detection**: Monitor and identify security incidents
- **Analysis**: Assess incident scope and impact
- **Containment**: Limit incident spread and damage
- **Eradication**: Remove threat and vulnerabilities
- **Recovery**: Restore normal operations
- **Lessons Learned**: Document and improve processes

### Incident Classification
- **Low**: Minimal business impact
- **Medium**: Moderate business impact
- **High**: Significant business impact
- **Critical**: Severe business impact requiring immediate action

## 6. BUSINESS CONTINUITY AND DISASTER RECOVERY

### Business Continuity Planning
- Business impact analysis (BIA)
- Risk assessment for continuity
- Continuity strategy development
- Plan implementation and testing
- Plan maintenance and updates

### Disaster Recovery
- Recovery time objectives (RTO)
- Recovery point objectives (RPO)
- Backup and restoration procedures
- Alternative site arrangements
- Communication plans

## 7. COMPLIANCE AND LEGAL REQUIREMENTS

### Legal and Regulatory Compliance
- Identification of applicable laws and regulations
- Compliance monitoring and assessment
- Legal risk management
- Intellectual property protection
- Privacy and data protection requirements

### Audit and Assessment
- Internal audit programs
- External audit coordination
- Compliance reporting
- Corrective action management
- Continuous monitoring

## 8. TRAINING AND AWARENESS

### Security Awareness Programs
- General security awareness training
- Role-specific security training
- Incident response training
- Regular security updates and communications
- Training effectiveness measurement

### Competency Requirements
- Security role competency definitions
- Training needs assessment
- Professional development programs
- Certification requirements
- Knowledge transfer processes

## 9. VENDOR AND THIRD-PARTY MANAGEMENT

### Supplier Security Management
- Supplier security assessment
- Contractual security requirements
- Ongoing supplier monitoring
- Incident notification requirements
- Service level agreements (SLAs)

### Cloud Security
- Cloud service provider assessment
- Data location and sovereignty
- Shared responsibility models
- Cloud security monitoring
- Data portability and deletion

## 10. EMERGING TECHNOLOGIES AND THREATS

### Modern Security Challenges
- Internet of Things (IoT) security
- Artificial Intelligence (AI) and Machine Learning (ML) security
- Mobile device security
- Social engineering attacks
- Advanced persistent threats (APTs)

### Zero Trust Architecture
- Never trust, always verify principle
- Least privilege access
- Micro-segmentation
- Continuous monitoring and validation
- Identity-centric security model
`;

// Export the reference for use in content script
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ISO27001_REFERENCE;
}
