// SOTATEK ISMS & Software Development Security Reference
// Based on actual organizational documents: "CÁC QUY ĐỊNH VỀ AN TOÀN THÔNG TIN" & "BẢO MẬT TRONG PHÁT TRIỂN PHẦN MỀM"

const SOTATEK_SECURITY_REFERENCE = `
# SOTATEK INFORMATION SECURITY MANAGEMENT SYSTEM (ISMS) REFERENCE

## 1. ĐỊNH NGHĨA AN TOÀN THÔNG TIN (Information Security Definition)

### Thông tin là gì? (What is Information?)
- Theo ISO/IEC 27001:2022: "Thông tin là một loại tài sản cũng như các tài sản khác đều có giá trị đối với một tổ chức và cần được bảo vệ thích hợp"
- Tài sản thông tin bao gồm: Thông tin của khách hàng, thông tin của ngân hàng, thông tin của các bên liên quan khác
- Thông tin tồn tại ở nhiều dạng: In ra giấy, thiết bị điện tử, email, hình ảnh/video, cuộc hội thoại, ki<PERSON><PERSON> thức/kinh nghiệm, website

### An toàn bảo mật (Information Security)
- Bảo vệ hệ thống thông tin (con người, dữ liệu, quy trình) chống lại các hành vi trái phép:
  * Truy cập trái phép
  * Sử dụng trái phép
  * Chỉnh sửa trái phép
  * Phá hủy
  * Làm lộ thông tin

### Ba tính chất cơ bản (CIA Triad)
- **Tính bí mật (Confidentiality)**: Thông tin chỉ được truy cập bởi những người được phép
- **Tính toàn vẹn (Integrity)**: Thông tin được duy trì chính xác và đầy đủ
- **Tính sẵn sàng (Availability)**: Thông tin có sẵn khi cần thiết

## 2. PHÂN LOẠI TÀI SẢN VÀ THÔNG TIN (Asset and Information Classification)

### Tài sản do SOTATEK quản lý
- **Tài sản con người**: Nhân viên và kiến thức của họ
- **Tài sản thông tin**: Dữ liệu và thông tin của công ty và khách hàng
- **Tài sản phần mềm**: Ứng dụng và hệ thống phần mềm
- **Tài sản dịch vụ**: Các dịch vụ IT và hạ tầng
- **Tài sản vật lý**: Thiết bị, máy tính, cơ sở vật chất

### Phân loại bảo mật thông tin (Information Classification)
- **Công khai**: Mở cho tất cả mọi người trong và ngoài Công ty
- **Nội bộ**: Chỉ dành cho nhân viên công ty SotaTek
- **Mật**: Cần bảo mật đối với các thành viên không liên quan
- **Tuyệt mật**: Chỉ những cá nhân được phép truy cập và phải ký cam kết bảo mật

### Quy định sử dụng thông tin
- Không chia sẻ thông tin dự án tới những người không liên quan đến dự án
- Cấm sử dụng các máy tính, thiết bị ngoài mục đích sử dụng cho dự án

## 3. BẢO MẬT TRONG PHÁT TRIỂN PHẦN MỀM (Software Development Security)

### Mục đích
- Đảm bảo an toàn an ninh thông tin được thực hiện trong vòng đời phát triển phần mềm cho các ứng dụng và hệ thống thông tin mà SotaTek chịu trách nhiệm phát triển

### Phạm vi áp dụng
- Tất cả các sản phẩm phần mềm dịch vụ được phát triển bởi khối sản xuất của SotaTek
- Các bộ phận chịu trách nhiệm quản lý hệ thống thông tin của SotaTek
- Toàn bộ sản phẩm thông tin do khách hàng cung cấp, mã nguồn mở mà khối sản xuất sử dụng

### Định nghĩa bảo mật phần mềm (Software Security Classification)
- **Nghiêm trọng**: Các lỗ hổng cần có biện pháp xử lý ngay
- **Trung bình**: Các lỗ hổng ít nghiêm trọng hơn, có thể xử lý sau nhưng cần lên kế hoạch xử lý
- **Thấp**: Các lỗ hổng kém nghiêm trọng, có thể chấp nhận được

### Vá lỗi bảo mật (Security Patching)
- Các hệ thống thông tin quản lý dự án của SotaTek phải được cài đặt các bản vá lỗi bảo mật để bảo vệ tài sản khỏi các lỗ hổng đã biết
- **Cập nhật tự động**: Tất cả các hệ thống phải được kích hoạt tính năng cập nhật phần mềm tự động cho các bản vá lỗi

### Bảo mật vòng đời phát triển phần mềm (Secure SDLC)
- **Review và Đánh giá**: Source code sau khi được phát triển cần phải được review, rà soát bởi người chịu trách nhiệm và người được phân công
- **Tool quản lý**: Tool quản lý, lưu trữ source code và các tài liệu liên quan đến source code phải được ghi chép cụ thể trong văn bản quản lý cấu hình của dự án

### Quy định về mã nguồn mở và thư viện bên thứ 3
- **Nếu khách hàng cho phép**: Dự án phải tuân theo thỏa thuận cấp phép của mã/thư viện đó và phải lấy mã cấp phép nếu cần
- **Nếu khách hàng không chỉ định**: Quản lý dự án có thể tạo báo cáo đánh giá DAR (decision analysis resolution) để lựa chọn mã/thư viện phù hợp
- **Nếu khách hàng không cho phép**: Dự án tuyệt đối không được sử dụng bất kỳ mã nguồn/thư viện bên ngoài nào

### Bảo vệ dữ liệu trong quá trình lưu trữ và bàn giao
- **Lưu trữ an toàn**: Các thông tin trước khi bàn giao cho khách hàng phải được tuyệt đối lưu trữ an toàn và chỉ phân quyền nhân viên dự án đó được phép truy cập và sử dụng
- **Bàn giao chính xác**: Khi bàn giao thông tin cho khách hàng, người chịu trách nhiệm bàn giao cần đảm bảo bàn giao đúng thông tin ứng với mốc bàn giao đã thống nhất với khách hàng trước đó

## 4. QUY ĐỊNH VỀ MÁY TÍNH (Computer Usage Regulations)

### Phạm vi áp dụng
- Máy tính để bàn, máy tính xách tay (laptop) tại Công ty SotaTek
- Máy tính thuộc sở hữu cá nhân

### Yêu cầu đối với máy tính sử dụng trong Công ty
- **Screensaver**: Phải được cài "Screensaver" tối đa 5 phút
- **Password**: Phải được sử dụng và bảo vệ mật khẩu an toàn
- **Phần mềm có bản quyền**: Chỉ được cài những phần mềm có bản quyền trong danh mục được phép
- **Antivirus**: Phải được cài Antivirus và update tự động
- **Kiểm soát**: Đảm bảo kiểm soát khi cho người khác mượn máy tính

### Quy định bảo mật máy tính công ty
- **Bị khóa quyền quản trị**: Quyền này do IT quản lý
- **Phần mềm chống mã độc**: Cài phần mềm giải pháp phòng chống mã độc quản trị tập trung do IT cài đặt để quản lý
- **Cấm can thiệp**: Các hành động cố tình can thiệp thay đổi quyền quản trị, thay đổi thông số không thông qua CNTT được xem là hành động xâm phạm đến an toàn thông tin

## 5. QUY ĐỊNH VỀ PASSWORD (Password Regulations)

### Phạm vi áp dụng
- Các loại mật khẩu tại tất cả các bộ phận liên quan đến hệ thống an toàn thông tin ISMS của Công ty SotaTek

### Quy định chung
- **Account duy nhất**: Account của mỗi user là duy nhất
- **Không dùng chung**: Users không dùng chung account của nhau
- **Báo cáo sự cố**: Gặp sự cố về mật khẩu, cần thông báo ngay cho IT hoặc người quản lý để được hỗ trợ

### Yêu cầu về mật khẩu
- **Độ dài tối thiểu**: Tối thiểu 8 ký tự
- **Thay đổi định kỳ**: <90 ngày
- **Độ phức tạp**: Mật khẩu phải chứa các ký tự bao gồm 3-4 yêu cầu: ký tự in hoa, ký tự in thường, ký tự số, các ký tự đặc biệt

### Trường hợp ngoại lệ
- **Mật khẩu hệ thống quản trị mạng**: 4 ký tự bằng số
- **Mật khẩu cho các thiết bị thuộc Hệ thống mạng** (load balance, router, Switch): thay đổi 6 tháng 1 lần

## 6. QUY ĐỊNH SỬ DỤNG EMAIL, MẠNG NỘI BỘ (Email and Network Usage)

### Sử dụng email công ty
- **Không cho mượn tài khoản**: Không cho người khác mượn tài khoản
- **Không sử dụng tài khoản người khác**: Không sử dụng tài khoản của người khác
- **Không gửi spam**: Không gửi spam email
- **Không mở link lạ**: Không mở bất kỳ link lạ, email lạ không rõ nguồn gốc, email phishing
- **Không vi phạm thuần phong mỹ tục**: Không gửi/nhận thông tin vi phạm thuần phong mỹ tục, an ninh quốc phòng
- **Xin phê duyệt**: Khi gửi email, cần có sự chấp thuận trước từ người phụ trách hoặc tự động CC để đảm bảo có bản ghi của nội dung email

## 5. INCIDENT MANAGEMENT

### Incident Response Process
- **Preparation**: Establish incident response team and procedures
- **Detection**: Monitor and identify security incidents
- **Analysis**: Assess incident scope and impact
- **Containment**: Limit incident spread and damage
- **Eradication**: Remove threat and vulnerabilities
- **Recovery**: Restore normal operations
- **Lessons Learned**: Document and improve processes

### Incident Classification
- **Low**: Minimal business impact
- **Medium**: Moderate business impact
- **High**: Significant business impact
- **Critical**: Severe business impact requiring immediate action

## 6. BUSINESS CONTINUITY AND DISASTER RECOVERY

### Business Continuity Planning
- Business impact analysis (BIA)
- Risk assessment for continuity
- Continuity strategy development
- Plan implementation and testing
- Plan maintenance and updates

### Disaster Recovery
- Recovery time objectives (RTO)
- Recovery point objectives (RPO)
- Backup and restoration procedures
- Alternative site arrangements
- Communication plans

## 7. COMPLIANCE AND LEGAL REQUIREMENTS

### Legal and Regulatory Compliance
- Identification of applicable laws and regulations
- Compliance monitoring and assessment
- Legal risk management
- Intellectual property protection
- Privacy and data protection requirements

### Audit and Assessment
- Internal audit programs
- External audit coordination
- Compliance reporting
- Corrective action management
- Continuous monitoring

## 8. TRAINING AND AWARENESS

### Security Awareness Programs
- General security awareness training
- Role-specific security training
- Incident response training
- Regular security updates and communications
- Training effectiveness measurement

### Competency Requirements
- Security role competency definitions
- Training needs assessment
- Professional development programs
- Certification requirements
- Knowledge transfer processes

## 9. VENDOR AND THIRD-PARTY MANAGEMENT

### Supplier Security Management
- Supplier security assessment
- Contractual security requirements
- Ongoing supplier monitoring
- Incident notification requirements
- Service level agreements (SLAs)

### Cloud Security
- Cloud service provider assessment
- Data location and sovereignty
- Shared responsibility models
- Cloud security monitoring
- Data portability and deletion

## 10. EMERGING TECHNOLOGIES AND THREATS

### Modern Security Challenges
- Internet of Things (IoT) security
- Artificial Intelligence (AI) and Machine Learning (ML) security
- Mobile device security
- Social engineering attacks
- Advanced persistent threats (APTs)

### Zero Trust Architecture
- Never trust, always verify principle
- Least privilege access
- Micro-segmentation
- Continuous monitoring and validation
- Identity-centric security model
`;

// Export the reference for use in content script
if (typeof module !== "undefined" && module.exports) {
  module.exports = ISO27001_REFERENCE;
}
